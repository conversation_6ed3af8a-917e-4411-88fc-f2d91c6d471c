'use client'

import React, { useEffect, useRef, useState } from 'react'
import { FiMessageCircle, FiSettings, FiCpu } from 'react-icons/fi'
import { Button } from '@/components/ui/button'
// import toast from 'react-hot-toast'
import { useTabStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'
import { generateAiConversationId } from '@/lib/id'
import { getAccessPermissions } from '@/app/configs/tier-permissions'
import { useSession } from 'next-auth/react'
import { SubscriptionTier } from '@prisma/client'
import { useAiConversationV2 } from '../../hooks/useAiConversationV2'
import ChatV2MessageList from '../chat/ChatV2MessageList'
import ChatV2Input from '../chat/ChatV2Input'
import type { Tab } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'
import {
  getChatApiEndpoint,
  getChatSystemVersion,
} from '@/app/configs/feature-flags'

type ChatTabContentV2Props = {
  tab: Tab
  dragTreeId: string
}

export default function ChatTabContentV2({
  tab,
  dragTreeId: _dragTreeId,
}: ChatTabContentV2Props) {
  const { data: session } = useSession()
  const { updateTabAiPaneData } = useTabStore()

  // Generate a provisional conversation ID client-side; we will create it server-side before use
  const conversationIdRef = useRef<string>(generateAiConversationId())
  const [createdServerConversationId, setCreatedServerConversationId] =
    useState<string | null>(null)

  // Get user permissions
  const userTier =
    (session?.user as any)?.subscriptionTier || SubscriptionTier.FREE
  const permissions = getAccessPermissions(userTier)

  // API endpoint - uses feature flag
  const apiEndpoint = getChatApiEndpoint()

  // Prefer server-created/tab-stored ID for readiness; fallback local ID is used only for initial POST
  const preferredConversationId =
    tab.aiPaneData?.conversationId || createdServerConversationId || null
  const conversationId = preferredConversationId || conversationIdRef.current
  const isConversationReady = Boolean(
    preferredConversationId && preferredConversationId.startsWith('thread_')
  )

  // Context content function
  const contextContent = () => {
    try {
      const contextIds = tab.aiPaneData?.contextIds || []
      if (contextIds.length === 0) return ''

      // Simple context placeholder for now
      return `Context from ${contextIds.length} documents`
    } catch (error) {
      console.error('Error getting context:', error)
      return ''
    }
  }

  // Ensure conversation row exists on the server as soon as we have an ID
  useEffect(() => {
    let cancelled = false
    async function ensureServerConversation() {
      // Only create once, and only if not already created in this session
      if (
        createdServerConversationId ||
        !conversationIdRef.current ||
        tab.aiPaneData?.conversationId
      )
        return
      try {
        const res = await fetch('/api/aipane/conversations', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            conversationId: conversationIdRef.current,
            title: 'Untitled chat',
            contextEntityType: 'drag_tree',
            contextEntityId: _dragTreeId,
            contextIds: tab.aiPaneData?.contextIds || [],
          }),
        })
        if (!res.ok) {
          // Respect plan limits and auth; do not bypass with client fallback
          if (res.status === 403) {
            // Show a soft error; user cannot create more chats for this tree
            console.warn('Chat limit reached for this drag tree')
            return
          }
          if (res.status === 401) {
            console.warn('Unauthorized to create conversation')
            return
          }
          // For transient errors, allow chat-v2 upsert to handle first turn
          return
        } else {
          const data = (await res.json()) as { conversationId: string }
          if (!cancelled && data?.conversationId) {
            // Store to tab state for future renders
            updateTabAiPaneData(tab.id, {
              conversationId: data.conversationId,
              model: tab.aiPaneData?.model || 'gpt-4.1',
              contextIds: tab.aiPaneData?.contextIds || [],
            })
            setCreatedServerConversationId(data.conversationId)
          }
        }
      } catch (_e) {
        // Ignore; chat-v2 API upsert covers the first turn as a fallback
      }
    }
    ensureServerConversation()
    return () => {
      cancelled = true
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Use the new conversation v2 hook
  const conversation = useAiConversationV2({
    conversationId: isConversationReady ? conversationId : undefined,
    apiEndpoint,
    model: tab.aiPaneData?.model || 'gpt-4.1',
    context: contextContent,
    contextIds: tab.aiPaneData?.contextIds || [],
    dragTreeId: _dragTreeId,
    tabId: tab.id,
    // Disable the hook until conversation is ready
    enabled: isConversationReady,
  })

  // Check if user can use chat
  if (!permissions.canCreateAiChat) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <FiMessageCircle className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <h2 className="text-lg font-medium text-gray-500 mb-2">
            Chat Not Available
          </h2>
          <p className="text-sm text-gray-400">
            Upgrade your plan to access AI chat functionality
          </p>
        </div>
      </div>
    )
  }

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    // sendMessage,
    conversation: conversationData,
    isLoadingConversation,
  } = conversation

  // Compute context count from tab store or conversation metadata
  let _meta: any = (conversationData as any)?.metadata
  if (typeof _meta === 'string') {
    try {
      _meta = JSON.parse(_meta)
    } catch {
      _meta = undefined
    }
  }
  const contextCount =
    tab.aiPaneData?.contextIds?.length ?? _meta?.contextIds?.length ?? 0

  // Handle start chat
  // No greeting flow; user begins by typing in the input above

  // Handle custom submit to ensure conversation ID is set
  const handleCustomSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!input.trim()) return

    // Ensure conversation ID is set in tab store (prefer server-created ID)
    if (!tab.aiPaneData?.conversationId) {
      updateTabAiPaneData(tab.id, {
        conversationId:
          createdServerConversationId || conversationIdRef.current,
        model: tab.aiPaneData?.model || 'gpt-4.1',
        contextIds: tab.aiPaneData?.contextIds || [],
      })
    }

    // Submit the message
    handleSubmit(e)
  }

  // Show loading state while conversation is being loaded
  // Guard with isConversationReady to avoid "forever loading" before the server ID exists
  // Also avoid showing spinner if we already have messages to render
  if (isConversationReady && isLoadingConversation && messages.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex flex-col items-center gap-2 text-gray-500 animate-pulse">
          <FiMessageCircle className="w-8 h-8" />
          <span className="text-sm">Loading conversation...</span>
        </div>
      </div>
    )
  }

  // Show empty state if no messages yet; remove greeting send. User must type then submit.
  if (messages.length === 0) {
    return (
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <FiMessageCircle className="w-5 h-5 text-blue-600" />
            <div>
              <h2 className="font-medium text-gray-900">AI Chat v2</h2>
              <p className="text-xs text-gray-500">
                {contextCount > 0
                  ? `Using ${contextCount} context document${contextCount === 1 ? '' : 's'}`
                  : 'No context documents selected'}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="text-gray-500 hover:text-gray-700"
          >
            <FiSettings className="w-4 h-4" />
          </Button>
        </div>

        {/* Empty state */}
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md">
            <FiCpu className="w-16 h-16 mx-auto mb-6 text-gray-300" />
            <h3 className="text-xl font-medium text-gray-900 mb-3">
              Start a Conversation
            </h3>
            <p className="text-gray-500 mb-6">
              Chat with AI to get help with your questions. The AI has access to
              web search and can provide comprehensive answers.
            </p>
            <ChatV2Input
              input={input}
              onInputChange={handleInputChange}
              onSubmit={handleCustomSubmit}
              disabled={!permissions.canCreateAiMessage}
              isLoading={isLoading}
              placeholder="Type your question and press Enter to start..."
              className="mt-2"
            />
            <p className="text-xs text-gray-400 mt-4">
              This is Chat {getChatSystemVersion()} with tool calling support
            </p>
          </div>
        </div>
      </div>
    )
  }

  // Show chat interface
  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <FiMessageCircle className="w-5 h-5 text-blue-600" />
          <div>
            <h2 className="font-medium text-gray-900">
              AI Chat {getChatSystemVersion()}
            </h2>
            <p className="text-xs text-gray-500">
              {contextCount > 0
                ? `Using ${contextCount} context document${contextCount === 1 ? '' : 's'}`
                : 'No context documents selected'}
            </p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="text-gray-500 hover:text-gray-700"
        >
          <FiSettings className="w-4 h-4" />
        </Button>
      </div>

      {/* Messages */}
      <ChatV2MessageList
        messages={messages}
        isLoading={isLoading}
        className="flex-1 min-h-0"
      />

      {/* Input */}
      <ChatV2Input
        input={input}
        onInputChange={handleInputChange}
        onSubmit={handleCustomSubmit}
        disabled={!permissions.canCreateAiMessage}
        isLoading={isLoading}
        placeholder="Message AI Assistant..."
      />
    </div>
  )
}
