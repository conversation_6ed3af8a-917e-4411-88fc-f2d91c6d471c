'use client'

import React from 'react'
import { FiMessageCircle, FiSettings, FiCpu } from 'react-icons/fi'
import { Button } from '@/components/ui/button'
import { getAccessPermissions } from '@/app/configs/tier-permissions'
import { useSession } from 'next-auth/react'
import { SubscriptionTier } from '@prisma/client'
import { ChatPaneProvider, useChatPane } from '../../chat-v2/ChatPaneContext'
import ChatV2MessageList from '../chat/ChatV2MessageList'
import ChatV2Input from '../chat/ChatV2Input'
import type { Tab } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'
import {
  getChatApiEndpoint,
  getChatSystemVersion,
} from '@/app/configs/feature-flags'

type ChatTabContentV2Props = {
  tab: Tab
  dragTreeId: string
}

// Internal component that uses the ChatPane context
function ChatTabContentV2Internal({ contextCount }: { contextCount: number }) {
  const chatPane = useChatPane()
  const { data: session } = useSession()

  // Get user permissions
  const userTier =
    (session?.user as any)?.subscriptionTier || SubscriptionTier.FREE
  const permissions = getAccessPermissions(userTier)

  // Check if user can use chat
  if (!permissions.canCreateAiChat) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <FiMessageCircle className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <h2 className="text-lg font-medium text-gray-500 mb-2">
            Chat Not Available
          </h2>
          <p className="text-sm text-gray-400">
            Upgrade your plan to access AI chat functionality
          </p>
        </div>
      </div>
    )
  }

  // Extract values from chat pane
  const { messages, input, setInput, submit, status, isStreaming } = chatPane

  // contextCount is passed as a prop from the parent

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value)
  }

  // Handle form submit
  const handleCustomSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    submit()
  }

  // Show loading state while conversation is being loaded
  // Only show spinner based on status, not message length to avoid forever loading
  if (status === 'loading-history') {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex flex-col items-center gap-2 text-gray-500 animate-pulse">
          <FiMessageCircle className="w-8 h-8" />
          <span className="text-sm">Loading conversation...</span>
        </div>
      </div>
    )
  }

  // Show empty state if no messages yet; remove greeting send. User must type then submit.
  if (messages.length === 0) {
    return (
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <FiMessageCircle className="w-5 h-5 text-blue-600" />
            <div>
              <h2 className="font-medium text-gray-900">AI Chat v2</h2>
              <p className="text-xs text-gray-500">
                {contextCount > 0
                  ? `Using ${contextCount} context document${contextCount === 1 ? '' : 's'}`
                  : 'No context documents selected'}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="text-gray-500 hover:text-gray-700"
          >
            <FiSettings className="w-4 h-4" />
          </Button>
        </div>

        {/* Empty state */}
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md">
            <FiCpu className="w-16 h-16 mx-auto mb-6 text-gray-300" />
            <h3 className="text-xl font-medium text-gray-900 mb-3">
              Start a Conversation
            </h3>
            <p className="text-gray-500 mb-6">
              Chat with AI to get help with your questions. The AI has access to
              web search and can provide comprehensive answers.
            </p>
            <ChatV2Input
              input={input}
              onInputChange={handleInputChange}
              onSubmit={handleCustomSubmit}
              disabled={!permissions.canCreateAiMessage}
              isLoading={isStreaming}
              placeholder="Type your question and press Enter to start..."
              className="mt-2"
            />
            <p className="text-xs text-gray-400 mt-4">
              This is Chat {getChatSystemVersion()} with tool calling support
            </p>
          </div>
        </div>
      </div>
    )
  }

  // Show chat interface
  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <FiMessageCircle className="w-5 h-5 text-blue-600" />
          <div>
            <h2 className="font-medium text-gray-900">
              AI Chat {getChatSystemVersion()}
            </h2>
            <p className="text-xs text-gray-500">
              {contextCount > 0
                ? `Using ${contextCount} context document${contextCount === 1 ? '' : 's'}`
                : 'No context documents selected'}
            </p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="text-gray-500 hover:text-gray-700"
        >
          <FiSettings className="w-4 h-4" />
        </Button>
      </div>

      {/* Messages */}
      <ChatV2MessageList
        messages={messages}
        isLoading={isStreaming}
        className="flex-1 min-h-0"
      />

      {/* Input */}
      <ChatV2Input
        input={input}
        onInputChange={handleInputChange}
        onSubmit={handleCustomSubmit}
        disabled={!permissions.canCreateAiMessage}
        isLoading={isStreaming}
        placeholder="Message AI Assistant..."
      />
    </div>
  )
}

// Main export component that wraps with provider
export default function ChatTabContentV2({
  tab,
  dragTreeId: _dragTreeId,
}: ChatTabContentV2Props) {
  // Context content function
  const contextContent = () => {
    try {
      const contextIds = tab.aiPaneData?.contextIds || []
      if (contextIds.length === 0) return ''

      // Simple context placeholder for now
      return `Context from ${contextIds.length} documents`
    } catch (error) {
      console.error('Error getting context:', error)
      return ''
    }
  }

  return (
    <ChatPaneProvider
      tabId={tab.id}
      dragTreeId={_dragTreeId}
      apiEndpoint={getChatApiEndpoint()}
      model={tab.aiPaneData?.model || 'gpt-4.1'}
      contextIds={tab.aiPaneData?.contextIds || []}
      getContext={contextContent}
      initialConversationId={tab.aiPaneData?.conversationId}
    >
      <ChatTabContentV2Internal
        contextCount={tab.aiPaneData?.contextIds?.length || 0}
      />
    </ChatPaneProvider>
  )
}
