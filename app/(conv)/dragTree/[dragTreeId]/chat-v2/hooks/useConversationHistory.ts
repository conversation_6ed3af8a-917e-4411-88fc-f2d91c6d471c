'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import type { UIMessage } from 'ai'
import { ENABLE_CHAT_DEBUG_LOGGING } from '@/app/configs/feature-flags'

export type ConversationMeta = {
  id: string
  title?: string
  createdAt: string
  updatedAt: string
  metadata?: any
}

export type UseConversationHistoryOptions = {
  conversationId?: string
  enabled?: boolean
  limit?: number
  includeSteps?: boolean
}

export type UseConversationHistoryReturn = {
  messages: UIMessage[] | null
  conversationMeta: ConversationMeta | null
  isLoading: boolean
  error: string | null
  refresh: (() => Promise<void>) | null
}

/**
 * Hook for fetching and managing conversation history separately from streaming.
 * 
 * Handles:
 * - Loading existing messages from the conversation API
 * - Converting database messages to UIMessage format
 * - Providing refresh functionality for post-stream updates
 * - Proper cleanup and abort handling
 */
export function useConversationHistory({
  conversationId,
  enabled = true,
  limit = 50,
  includeSteps = false,
}: UseConversationHistoryOptions): UseConversationHistoryReturn {
  const [messages, setMessages] = useState<UIMessage[] | null>(null)
  const [conversationMeta, setConversationMeta] = useState<ConversationMeta | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  
  const isMountedRef = useRef(true)
  const abortControllerRef = useRef<AbortController | null>(null)

  // Load conversation history
  const loadHistory = useCallback(async () => {
    if (!conversationId || !enabled) {
      setMessages(null)
      setConversationMeta(null)
      setIsLoading(false)
      setError(null)
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // Abort any existing request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      abortControllerRef.current = new AbortController()

      // Build API URL with query parameters
      const url = new URL(
        `/api/aipane/conversations/${conversationId}`,
        window.location.origin
      )
      url.searchParams.set('limit', limit.toString())
      url.searchParams.set('includeSteps', includeSteps.toString())

      if (ENABLE_CHAT_DEBUG_LOGGING) {
        console.log('📚 [useConversationHistory] Loading history:', {
          conversationId,
          limit,
          includeSteps,
        })
      }

      const response = await fetch(url.toString(), {
        signal: abortControllerRef.current.signal,
      })

      if (!isMountedRef.current) return

      if (response.ok) {
        const data = await response.json()
        const existingMessages = data.messages || []

        // Convert database messages to UIMessage format
        // Filter out system messages as they're not displayed in the UI
        const uiMessages: UIMessage[] = existingMessages
          .filter((msg: any) => msg.role !== 'SYSTEM')
          .map((msg: any) => ({
            id: msg.id || `msg_${Date.now()}_${Math.random()}`,
            role: (msg.role || '').toLowerCase() as 'user' | 'assistant',
            parts: [{ type: 'text', text: msg.content }],
          }))

        setMessages(uiMessages)
        setConversationMeta(data.conversation || null)

        if (ENABLE_CHAT_DEBUG_LOGGING) {
          console.log(
            `📚 [useConversationHistory] Loaded ${uiMessages.length} messages for ${conversationId}`
          )
        }
      } else if (response.status === 404) {
        // Conversation doesn't exist yet - this is normal for new conversations
        setMessages([])
        setConversationMeta(null)
        
        if (ENABLE_CHAT_DEBUG_LOGGING) {
          console.log('📚 [useConversationHistory] Conversation not found (new conversation)')
        }
      } else {
        throw new Error(`Failed to load conversation: ${response.status}`)
      }
    } catch (err: any) {
      if (err.name === 'AbortError') {
        // Request was aborted, don't update state
        return
      }

      console.error('❌ [useConversationHistory] Failed to load history:', err)
      
      if (isMountedRef.current) {
        setError(err.message || 'Failed to load conversation history')
        setMessages(null)
        setConversationMeta(null)
      }
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false)
      }
    }
  }, [conversationId, enabled, limit, includeSteps])

  // Load history when conversation ID or options change
  useEffect(() => {
    loadHistory()
  }, [loadHistory])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  // Return null refresh function if not enabled
  const refresh = enabled ? loadHistory : null

  return {
    messages,
    conversationMeta,
    isLoading,
    error,
    refresh,
  }
}
