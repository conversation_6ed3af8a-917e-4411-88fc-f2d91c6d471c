'use client'

import React, {
  createContext,
  useContext,
  useReducer,
  useEffect,
  useCallback,
  useRef,
} from 'react'
import type { UIMessage } from 'ai'
import { generateAiConversationId } from '@/lib/id'
import { useTabStore } from '../stores/useTabStore'
import { useChatStreamBridge } from './hooks/useChatStreamBridge'
import { useConversationHistory } from './hooks/useConversationHistory'

// Types
export type ChatPaneStatus =
  | 'idle' // Initial state, no conversation ID
  | 'creating' // Generating conversation ID
  | 'loading-history' // Loading existing messages
  | 'ready' // Ready to send/receive messages
  | 'streaming' // Currently streaming a response
  | 'error' // Error state

export type ChatPaneState = {
  conversationId?: string
  status: ChatPaneStatus
  messages: UIMessage[]
  input: string
  isStreaming: boolean
  error?: string
}

export type ChatPaneActions = {
  setInput: (value: string) => void
  submit: () => void
  refresh: () => Promise<void>
}

// Action types for reducer
type ChatPaneAction =
  | { type: 'RESOLVE_ID'; conversationId: string }
  | { type: 'LOAD_HISTORY_START' }
  | { type: 'LOAD_HISTORY_SUCCESS'; messages: UIMessage[] }
  | { type: 'LOAD_HISTORY_ERROR'; error: string }
  | { type: 'SET_INPUT'; input: string }
  | { type: 'STREAM_START' }
  | { type: 'STREAM_FINISH' }
  | { type: 'UPDATE_MESSAGES'; messages: UIMessage[] }
  | { type: 'ERROR'; error: string }
  | { type: 'RESET_ERROR' }

// Reducer
function chatPaneReducer(
  state: ChatPaneState,
  action: ChatPaneAction
): ChatPaneState {
  switch (action.type) {
    case 'RESOLVE_ID':
      return {
        ...state,
        conversationId: action.conversationId,
        status: 'loading-history',
        error: undefined,
      }

    case 'LOAD_HISTORY_START':
      return {
        ...state,
        status: 'loading-history',
        error: undefined,
      }

    case 'LOAD_HISTORY_SUCCESS':
      return {
        ...state,
        status: 'ready',
        messages: action.messages,
        error: undefined,
      }

    case 'LOAD_HISTORY_ERROR':
      return {
        ...state,
        status: 'error',
        error: action.error,
      }

    case 'SET_INPUT':
      return {
        ...state,
        input: action.input,
      }

    case 'STREAM_START':
      return {
        ...state,
        status: 'streaming',
        isStreaming: true,
        error: undefined,
      }

    case 'STREAM_FINISH':
      return {
        ...state,
        status: 'ready',
        isStreaming: false,
      }

    case 'UPDATE_MESSAGES':
      return {
        ...state,
        messages: action.messages,
      }

    case 'ERROR':
      return {
        ...state,
        status: 'error',
        error: action.error,
        isStreaming: false,
      }

    case 'RESET_ERROR':
      return {
        ...state,
        error: undefined,
        status: state.conversationId ? 'ready' : 'idle',
      }

    default:
      return state
  }
}

// Context
const ChatPaneContext = createContext<{
  state: ChatPaneState
  actions: ChatPaneActions
} | null>(null)

// Provider props
export type ChatPaneProviderProps = {
  tabId: string
  dragTreeId: string
  apiEndpoint: string
  model: string
  contextIds: string[]
  getContext: () => string
  initialConversationId?: string
  children: React.ReactNode
}

// Provider component
export function ChatPaneProvider({
  tabId,
  dragTreeId: _dragTreeId,
  apiEndpoint,
  model,
  contextIds,
  getContext,
  initialConversationId,
  children,
}: ChatPaneProviderProps) {
  const { updateTabAiPaneData } = useTabStore()

  // Initialize state
  const [state, dispatch] = useReducer(chatPaneReducer, {
    conversationId: initialConversationId,
    status: initialConversationId ? 'loading-history' : 'idle',
    messages: [],
    input: '',
    isStreaming: false,
  })

  // History hook - only mount when we have a conversation ID
  const historyHook = useConversationHistory({
    conversationId: state.conversationId,
    enabled: Boolean(state.conversationId),
  })

  // Stream bridge hook - only mount when we have a conversation ID
  const streamHook = useChatStreamBridge({
    conversationId: state.conversationId,
    apiEndpoint,
    model,
    contextIds,
    getContext,
    enabled: Boolean(state.conversationId),
  })

  // Handle history loading
  useEffect(() => {
    if (!state.conversationId || state.status !== 'loading-history') return

    if (historyHook.isLoading) {
      dispatch({ type: 'LOAD_HISTORY_START' })
    } else if (historyHook.error) {
      dispatch({ type: 'LOAD_HISTORY_ERROR', error: historyHook.error })
    } else if (historyHook.messages) {
      dispatch({ type: 'LOAD_HISTORY_SUCCESS', messages: historyHook.messages })
    }
  }, [
    historyHook.isLoading,
    historyHook.error,
    historyHook.messages,
    state.conversationId,
    state.status,
  ])

  // Handle streaming state
  useEffect(() => {
    if (streamHook.isStreaming && state.status !== 'streaming') {
      dispatch({ type: 'STREAM_START' })
    } else if (!streamHook.isStreaming && state.status === 'streaming') {
      dispatch({ type: 'STREAM_FINISH' })
    }
  }, [streamHook.isStreaming, state.status])

  // Handle streaming errors
  useEffect(() => {
    if (streamHook.error) {
      dispatch({ type: 'ERROR', error: streamHook.error.message })
    }
  }, [streamHook.error])

  // Combine history and streaming messages
  useEffect(() => {
    const historyMessages = historyHook.messages || []
    const streamingMessages = streamHook.messages || []
    const combined = [...historyMessages, ...streamingMessages]

    if (
      combined.length !== state.messages.length ||
      combined.some((msg, i) => msg.id !== state.messages[i]?.id)
    ) {
      dispatch({ type: 'UPDATE_MESSAGES', messages: combined })
    }
  }, [historyHook.messages, streamHook.messages, state.messages])

  // Track pending message for auto-send after ID resolution
  const pendingMessageRef = useRef<string>('')

  // Actions
  const actions: ChatPaneActions = {
    setInput: useCallback((value: string) => {
      dispatch({ type: 'SET_INPUT', input: value })
    }, []),

    submit: useCallback(() => {
      if (!state.input.trim()) return

      // If no conversation ID, generate one and store the message for later
      if (!state.conversationId) {
        const newId = generateAiConversationId()
        pendingMessageRef.current = state.input

        dispatch({ type: 'RESOLVE_ID', conversationId: newId })
        dispatch({ type: 'SET_INPUT', input: '' })

        // Update tab store with new conversation ID
        updateTabAiPaneData(tabId, {
          conversationId: newId,
          model,
          contextIds,
        })

        return
      }

      // Send message through stream hook
      if (streamHook.sendMessage) {
        streamHook.sendMessage({ text: state.input })
        dispatch({ type: 'SET_INPUT', input: '' })
      }
    }, [
      state.input,
      state.conversationId,
      streamHook.sendMessage,
      updateTabAiPaneData,
      tabId,
      model,
      contextIds,
    ]),

    refresh: useCallback(async () => {
      if (historyHook.refresh) {
        await historyHook.refresh()
      }
    }, [historyHook.refresh]),
  }

  // Auto-send pending message after ID resolution and history loading
  useEffect(() => {
    if (
      state.conversationId &&
      state.status === 'ready' &&
      pendingMessageRef.current &&
      streamHook.sendMessage
    ) {
      const messageToSend = pendingMessageRef.current
      pendingMessageRef.current = ''
      streamHook.sendMessage({ text: messageToSend })
    }
  }, [state.conversationId, state.status, streamHook.sendMessage])

  // Refresh history after streaming finishes
  useEffect(() => {
    if (state.status === 'ready' && !state.isStreaming && historyHook.refresh) {
      // Small delay to ensure server has processed the message
      const timer = setTimeout(() => {
        historyHook.refresh?.()
      }, 200)
      return () => clearTimeout(timer)
    }
  }, [state.status, state.isStreaming, historyHook.refresh])

  return (
    <ChatPaneContext.Provider value={{ state, actions }}>
      {children}
    </ChatPaneContext.Provider>
  )
}

// Hooks for consuming the context
export function useChatPaneState(): ChatPaneState {
  const context = useContext(ChatPaneContext)
  if (!context) {
    throw new Error('useChatPaneState must be used within a ChatPaneProvider')
  }
  return context.state
}

export function useChatPaneActions(): ChatPaneActions {
  const context = useContext(ChatPaneContext)
  if (!context) {
    throw new Error('useChatPaneActions must be used within a ChatPaneProvider')
  }
  return context.actions
}

export function useChatPane(): ChatPaneState & ChatPaneActions {
  const state = useChatPaneState()
  const actions = useChatPaneActions()
  return { ...state, ...actions }
}
