/**
 * Unit tests for AI Pane Chat v2 API route logic
 * @jest-environment node
 */

import {
  RETRY_CONFIG,
  RetryStrategy,
  computeBackoffDelay,
} from '@/app/api/aipane/chat/chat-config'

// Minimal helpers to simulate route logic decisions
const isFirstTurn = (existingCount: number) => existingCount === 0

// Fake persistence functions we will "mock" in tests
async function fakeFinalizeConversationTurn(..._args: any[]) {
  return { success: true }
}
async function fakePersistConversationTurn(..._args: any[]) {
  return { success: true }
}

// Simulate the retry loop used by chat-v2 route
async function persistWithRetry(options: {
  firstTurn: boolean
  hasContext: boolean
  finalize: () => Promise<{ success: boolean }>
  persist: () => Promise<{ success: boolean }>
  strategy?: RetryStrategy
  maxRetries?: number
  baseMs?: number
}) {
  const {
    firstTurn,
    hasContext,
    finalize,
    persist,
    strategy = RETRY_CONFIG.STRATEGY,
    maxRetries = RETRY_CONFIG.MAX_RETRIES,
    baseMs = RETRY_CONFIG.BACKOFF_MS,
  } = options
  let attempt = 0
  let ok = false
  let lastError: any = null

  while (attempt < maxRetries && !ok) {
    attempt++
    try {
      if (firstTurn && hasContext) {
        const res = await finalize()
        ok = res.success
      } else {
        const res = await persist()
        ok = res.success
      }
      if (!ok) {
        const ms = computeBackoffDelay(attempt, baseMs, strategy)
        await new Promise(r => setTimeout(r, ms))
      }
    } catch (err) {
      lastError = err
      const ms = computeBackoffDelay(attempt, baseMs, strategy)
      await new Promise(r => setTimeout(r, ms))
    }
  }
  return { ok, lastError, attempts: attempt }
}

describe('AI Pane Chat v2 - Core logic', () => {
  describe('First-turn context persistence', () => {
    it('uses finalizeConversationTurn when first turn has context', async () => {
      const finalizeSpy = jest.fn().mockResolvedValue({ success: true })
      const persistSpy = jest.fn().mockResolvedValue({ success: true })

      const result = await persistWithRetry({
        firstTurn: isFirstTurn(0),
        hasContext: true,
        finalize: finalizeSpy,
        persist: persistSpy,
      })

      expect(result.ok).toBe(true)
      expect(finalizeSpy).toHaveBeenCalledTimes(1)
      expect(persistSpy).not.toHaveBeenCalled()
    })

    it('falls back to persistConversationTurn when no context on first turn', async () => {
      const finalizeSpy = jest.fn().mockResolvedValue({ success: true })
      const persistSpy = jest.fn().mockResolvedValue({ success: true })

      const result = await persistWithRetry({
        firstTurn: isFirstTurn(0),
        hasContext: false,
        finalize: finalizeSpy,
        persist: persistSpy,
      })

      expect(result.ok).toBe(true)
      expect(finalizeSpy).not.toHaveBeenCalled()
      expect(persistSpy).toHaveBeenCalledTimes(1)
    })
  })

  describe('Subsequent turn persistence', () => {
    it('uses persistConversationTurn on non-first turns', async () => {
      const finalizeSpy = jest.fn().mockResolvedValue({ success: true })
      const persistSpy = jest.fn().mockResolvedValue({ success: true })

      const result = await persistWithRetry({
        firstTurn: isFirstTurn(3),
        hasContext: true,
        finalize: finalizeSpy,
        persist: persistSpy,
      })

      expect(result.ok).toBe(true)
      expect(finalizeSpy).not.toHaveBeenCalled()
      expect(persistSpy).toHaveBeenCalledTimes(1)
    })
  })

  describe('Retry and backoff strategies', () => {
    it('retries and succeeds on second attempt (exponential default)', async () => {
      jest.useFakeTimers()
      const finalizeSpy = jest.fn().mockResolvedValue({ success: false })
      const persistSpy = jest
        .fn()
        .mockResolvedValueOnce({ success: false })
        .mockResolvedValueOnce({ success: true })

      const promise = persistWithRetry({
        firstTurn: false,
        hasContext: false,
        finalize: finalizeSpy,
        persist: persistSpy,
        strategy: RetryStrategy.EXPONENTIAL,
        baseMs: 10,
        maxRetries: 3,
      })

      // Advance timers: first backoff (10ms), then second (20ms). It should succeed on second try.
      await jest.advanceTimersByTimeAsync(10)
      await jest.advanceTimersByTimeAsync(20)
      const result = await promise

      expect(result.ok).toBe(true)
      expect(persistSpy).toHaveBeenCalledTimes(2)
      jest.useRealTimers()
    })

    it('supports linear backoff when configured', async () => {
      jest.useFakeTimers()
      const persistSpy = jest
        .fn()
        .mockResolvedValueOnce({ success: false })
        .mockResolvedValueOnce({ success: false })
        .mockResolvedValueOnce({ success: true })

      const promise = persistWithRetry({
        firstTurn: false,
        hasContext: false,
        finalize: jest.fn(),
        persist: persistSpy,
        strategy: RetryStrategy.LINEAR,
        baseMs: 10,
        maxRetries: 3,
      })

      // Linear waits: 10ms (attempt 1), 20ms (attempt 2)
      await jest.advanceTimersByTimeAsync(10)
      await jest.advanceTimersByTimeAsync(20)
      const result = await promise

      expect(result.ok).toBe(true)
      expect(persistSpy).toHaveBeenCalledTimes(3)
      jest.useRealTimers()
    })

    it('computeBackoffDelay returns expected values', () => {
      expect(computeBackoffDelay(1, 50, RetryStrategy.EXPONENTIAL)).toBe(50)
      expect(computeBackoffDelay(2, 50, RetryStrategy.EXPONENTIAL)).toBe(100)
      expect(computeBackoffDelay(3, 50, RetryStrategy.EXPONENTIAL)).toBe(200)

      expect(computeBackoffDelay(1, 50, RetryStrategy.LINEAR)).toBe(50)
      expect(computeBackoffDelay(2, 50, RetryStrategy.LINEAR)).toBe(100)
      expect(computeBackoffDelay(3, 50, RetryStrategy.LINEAR)).toBe(150)
    })
  })
})
